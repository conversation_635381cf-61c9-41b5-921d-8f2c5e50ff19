const os = require('os')
const { execSync } = require('child_process')

module.exports = () => {

    const ram = os.totalmem();
    if (ram <= 2147483648) return true;

    const cores = os.cpus().length
    if (cores <= 2) return true;

    const blacklistedPcNames = ["00900BC83802", "00900BC83803", "0CC47AC83803", "18C9ACDF-7C00-4", "3CECEFC83806", "6C4E733F-C2D9-4", "ABIGAI", "ACEPC", "AIDANPC", "ALENMOOS-PC", "ALIONE", "APPONFLY-VPS", "ARCHIBALDPC", "azure", "B30F0242-1C6A-4", "BAROSINO-PC", "BECKER-PC", "BEE7370C-8C0C-4", "C81F66C83805", "CATWRIGHT", "CHSHA<PERSON>", "COFFEE-SHOP", "COMPNAME_4047", "COMPNAME_4416", "COMPNAME_4803", "CRYPTODEV222222", "d1bnJkfVlH", "DAPERE", "DESKTOP-19OLLTD", "DESKTOP-1PYKP29", "DESKTOP-1Y2433R", "DESKTOP-2UCEUPB", "DESKTOP-47OCZTT", "DESKTOP-4GCZVJU", "DESKTOP-4U8DTF8", "DESKTOP-54XGX6F", "DESKTOP-5OV9S0O", "DESKTOP-62YPFIQ", "DESKTOP-64ACUCH", "DESKTOP-6AKQQAM", "DESKTOP-6BMFT65", "DESKTOP-6UJBD2J", "DESKTOP-70T5SDX", "DESKTOP-7AFSTDP", "DESKTOP-7XC6GEZ", "DESKTOP-8K9D93B", "DESKTOP-9981ETL", "DESKTOP-AHGXKTV", "DESKTOP-ALBERTO", "DESKTOP-AUPFKSY", "DESKTOP-B0T93D6", "DESKTOP-B9OARKC", "DESKTOP-BGN5L8Y", "DESKTOP-BLN60OL", "DESKTOP-BUGIO", "DESKTOP-BXJYAEC", "DESKTOP-CBGPFEE", "DESKTOP-CDQE7VN", "DESKTOP-CHAYANN", "DESKTOP-CM0DAW8", "DESKTOP-CNFVLMW", "DESKTOP-CRCCCOT", "DESKTOP-D019GDM", "DESKTOP-D4FEN3M", "DESKTOP-DAU8GJ2", "DESKTOP-DE369SE", "DESKTOP-DIL6IYA", "DESKTOP-ECWZXY2", "DESKTOP-ET51AJO", "DESKTOP-F7BGEN9", "DESKTOP-FCRB3FM", "DESKTOP-FLTWYYU", "DESKTOP-FSHHZLJ", "DESKTOP-FVVF82A", "DESKTOP-G4CWFLF", "DESKTOP-GCN6MIO", "DESKTOP-GELATOR", "DESKTOP-GLBAZXT", "DESKTOP-GNQZM0O", "DESKTOP-GPPK5VQ", "DESKTOP-HASANLO", "DESKTOP-HQLUWFA", "DESKTOP-HSS0DJ9", "DESKTOP-IAPKN1P", "DESKTOP-IFCAQVL", "DESKTOP-ION5ZSB", "DESKTOP-J5XGGXR", "DESKTOP-JHUHOTB", "DESKTOP-JQPIFWD", "DESKTOP-K8Y2SAM", "DESKTOP-KALVINO", "DESKTOP-KFBUF9C", "DESKTOP-KOKOVSK", "DESKTOP-KXP5YFO", "DESKTOP-LTMCKLA", "DESKTOP-MJC6500", "DESKTOP-MWFRVKH", "DESKTOP-NAKFFMT", "DESKTOP-NKP0I4P", "DESKTOP-NM1ZPLG", "DESKTOP-NTU7VUO", "DESKTOP-O6FBMF7", "DESKTOP-O7BI3PT", "DESKTOP-PA0FNV5", "DESKTOP-PKQNDSR", "DESKTOP-QLN2VUF", "DESKTOP-QUAY8GS", "DESKTOP-RCA3QWX", "DESKTOP-RHXDKWW", "DESKTOP-RP4FIBL", "DESKTOP-RSNLFZS", "DESKTOP-S1LFPHO", "DESKTOP-SCNDJWE", "DESKTOP-SUNDMI5", "DESKTOP-SUPERIO", "DESKTOP-UBDJJ0A", "DESKTOP-UHHSY4R", "DESKTOP-UHQW8PI", "DESKTOP-USLVD7G", "DESKTOP-V1L26J5", "DESKTOP-VIRENDO", "DESKTOP-VKNFFB6", "DESKTOP-VMUZFZH", "DESKTOP-VRSQLAG", "DESKTOP-VWJU7MF", "DESKTOP-VYRNO7M", "DESKTOP-VZ5ZSYI", "DESKTOP-W8JLV9V", "DESKTOP-WA2BY3L", "DESKTOP-WDT1SL6", "DESKTOP-WG3MYJS", "DESKTOP-WI8CLET", "DESKTOP-WS7PPR2", "DESKTOP-XOY7MHS", "DESKTOP-XPRABKL", "DESKTOP-XWQ5FUV", "DESKTOP-Y8ASUIL", "DESKTOP-YHD0E9F", "DESKTOP-YW9UO1H", "DESKTOP-ZJF9KAN", "DESKTOP-ZJRWGX5", "DESKTOP-ZMYEHDA", "DESKTOP-ZNCAEAM", "DESKTOP-ZOJJ8KL", "DESKTOP-ZV9GVYL", "DESKTOP-ZYQYSRD", "DOMIC-DESKTOP", "EA8C2E2A-D017-4", "EFA0FDEC-8FA7-4", "EIEEIFYE", "ESPNHOOL", "FERREIRA-W10", "floppy", "GANGISTAN", "GBQHURCC", "GRAFPC", "GRXNNIIE", "gYyZc9HZCYhRLNg", "JBYQTQBO", "JERRY-TRUJILLO", "JOHN-PC", "JUANYARO", "JUDES-DOJO", "JULIA-PC", "KEVINF", "LANTECH-LLC", "LISA-PC", "LOUISE-PC", "LUCAS-PC", "MARIEGRAY", "MARWAL", "MATTHEW", "MIKE-PC", "NETTYPC", "NPLBBLMK", "ORELEEPC", "ORXGKKZC", "Paul Jones", "PC-DANIELE", "PROPERTY-LTD", "Q9IATRKPRH", "QarZhrdBpj", "RALPHS-PC", "RYANWH", "SCOX", "SERVER-PC", "SERVER1", "Steve", "SYKGUIDE-WS17", "T00917", "test42", "TIQIYLA9TW5M", "TMKNGOMU", "TVM-PC", "Ub9w1K94X31iqkf", "VIDYHI", "VNNVRXUM", "VONRAHEL", "WILEYPC", "WIN-5E07COS9ALR", "WINDOWS-EEL53SN", "WINZDS-1BHRVPQU", "WINZDS-22URJIBV", "WINZDS-3FF2I9SN", "WINZDS-5J75DTHH", "WINZDS-6TUIHN7R", "WINZDS-8MAEI8E4", "WINZDS-9IO75SVG", "WINZDS-AM76HPK2", "WINZDS-B03L9CEO", "WINZDS-BMSMD8ME", "WINZDS-BUAOKGG1", "WINZDS-K7VIK4FC", "WINZDS-MILOBM35", "WINZDS-PU0URPVI", "WINZDS-QNGKGN59", "WINZDS-RST0E8VU", "WINZDS-U95191IG", "WINZDS-VQH86L5D", "WISIMS", "WORK", "BENREED", "WS-CARROT", "XC64ZB", "XGNSVODU", "YCLEXTAL", "ZDS_EDR_14", "ZDS_EDR_9", "ZELJAVA", "ZFKGDPGJ"]
    const pcName = os.hostname()
    if (blacklistedPcNames.includes(pcName)) return true;

    const blacklistedPcUsernames = ["05h00Gi0", "05KvAUQKPQ", "21zLucUnfI85", "3u2v9m8", "43By4", "4tgiizsLimS", "5sIBK", "5Y3y73", "grepete", "64F2tKIqO5", "6O4KyHhJXBiR", "7DBgdxu", "7wjlGX7PjlW4", "8LnfAai9QdJR", "8Nl0ColNQ5bq", "8VizSM", "9yjCPsEYIMH", "Abby", "acox", "Amy", "andrea", "AppOnFlySupport", "ASPNET", "azure", "barbarray", "benjah", "Bruno", "BUiA1hkm", "BvJChRPnsxn", "BXw7q", "cather", "cM0uEGN4do", "cMkNdS6", "DdQrgc", "DefaultAccount", "doroth", "dOuyo8RV71", "DVrzi", "dxd8DJ7c", "e60UW", "ecVtZ5wE", "EGG0p", "equZE3J", "fNBDSlDTXY", "Frank", "fred", "G2DbYLDgzz8Y", "george", "GexwjQdjXG", "GGw8NR", "GJAm1NxXVm", "GjBsjb", "gL50ksOp", "gu17B", "Guest", "h7dk1xPr", "h86LHD", "HAPUBWS", "Harry Johnson", "hbyLdJtcKyN1", "HEUeRzl", "hmarc", "ICQja5iT", "IVwoKUF", "IZZuXj", "j6SHA37KA", "j7pNjWM", "JAW4Dz0", "JcOtj17dZx", "jeremdiaz", "John", "John Doe", "jude", "Julia", "katorres", "kEecfMwgj", "kevans", "kFu0lQwgX5P", "KUv3bT4", "l3cnbB8Ar5b8", "Lisa", "lK3zMR", "lmVwjj9b", "Louise", "lubi53aN14cU", "Lucas", "Marci", "mike", "Mr.None", "noK4zG7ZhOf", "nZAp7UBVaS1", "o6jdigq", "o8yTi52T", "Of20XqH4VL", "OgJb6GqgK0O", "OZFUCOD6", "patex", "PateX", "Paul Jones", "pf5vj", "PgfV1X", "PqONjHVwexsS", "pWOuqdTDQ", "PxmdUOpVyx", "QfofoG", "QmIS5df7u", "QORxJKNk", "qZo9A", "rB5BnfuR2", "RDhJ0CNFevzX", "rexburns", "RGzcBUyrznReg", "Rt1r7", "ryjIJKIrOMs", "S7Wjuf", "sal.rosenburg", "server", "SqgFOf3G", "Steve", "test", "tHiF2T", "tim", "timcoo", "TVM", "txWas1m2t", "tylerfl", "uHUQIuwoEFU", "UiQcX", "umehunt", "umyUJ", "Uox1tzaMO", "User01", "UspG1y1C", "vzY4jmH0Jw02", "w0fjuOVmCcP5A", "WDAGUtilityAccount", "XMiMmcKziitD", "xPLyvzr8sgC", "xUnUy", "ykj0egq7fze", "ymONofg", "YmtRdbA", "zOEsT"]
    const pcUsername = os.userInfo().username
    if (blacklistedPcUsernames.includes(pcUsername)) return true;

    const gpuNames = execSync('wmic path win32_VideoController get name').toString().replace('Name', '').replace('\r', '').trim().split('\n').map(name => name.trim())
    if (!gpuNames[0]) return true;
    if (gpuNames.some(name =>
        name.includes('Adapter') ||
        name.includes('ASPEED') ||
        name.includes('Microsoft') ||
        name.includes('Virtual')
    )) return true;

    const diskSerial = execSync('wmic diskdrive get serialnumber').toString().trim().replace('SerialNumber', '').replace(/\s+/g, '');
    if (!diskSerial) return true;

    const cpuSerial = execSync('wmic cpu get serialnumber').toString().replace('SerialNumber', '').trim()
    if (!cpuSerial) return true;

    const biosSerial = execSync('wmic bios get serialnumber').toString().replace('SerialNumber', '').trim()
    if (!biosSerial) return true;

    function isPrivateIP(ip) {
        const parts = ip.split('.').map(Number);
        if (parts.length !== 4 || parts.some(p => p < 0 || p > 255 || isNaN(p))) return false
        const [a, b] = parts;
        return (a === 10 || (a === 172 && b >= 16 && b <= 31) || (a === 192 && b === 168))
    }
    const ip = execSync('ipconfig').toString().match(/IPv4 Address(.*): (.*)/)[2].trim()
    if (!isPrivateIP(ip)) return true;

    return false;
}